package com.linkcircle.boss.module.billing.web.detail.rate.cost.mapper;

import com.linkcircle.boss.framework.mybatis.core.mapper.BaseMapperX;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.entity.CostPackageRateUsageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-25 10:31
 * @description 成本-套餐费率用量表数据访问层
 */
@Mapper
public interface CostPackageRateUsageMapper extends BaseMapperX<CostPackageRateUsageDO> {

    /**
     * 累加总用量
     */
    @Update("UPDATE cost_package_rate_usage SET total_usage = total_usage + #{usage} WHERE service_id = #{serviceId} AND purchase_id = #{purchaseId} AND billing_cycle = #{billingCycle}")
    int updateTotalUsageByCondition(@Param("serviceId") Long serviceId,
                                   @Param("purchaseId") Long purchaseId,
                                   @Param("billingCycle") String billingCycle,
                                   @Param("usage") BigDecimal usage);

}
