package com.linkcircle.boss.module.billing.web.detail.rate.cost.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.linkcircle.boss.framework.common.util.json.JsonUtils;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.mapper.CostFixedRateCycleStatusMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.mapper.CostPackageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.mapper.CostTierRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.mapper.CostUsageRateUsageMapper;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.entity.CostFixedRateCycleStatusDO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.entity.CostPackageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.entity.CostTierRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.entity.CostUsageRateUsageDO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.vo.RateUsageVO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.service.CostRateUsageManageService;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-06-25 10:31
 * @description 成本-费率用量管理服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CostRateUsageManageServiceImpl implements CostRateUsageManageService {

    private final CostFixedRateCycleStatusMapper costFixedRateCycleStatusMapper;
    private final CostTierRateUsageMapper costTierRateUsageMapper;
    private final CostPackageRateUsageMapper costPackageRateUsageMapper;
    private final CostUsageRateUsageMapper costUsageRateUsageMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                   BigDecimal usage,
                                   String usageUnit,
                                   Boolean existRecord) {
        ChargeRateTypeEnum rateType = cyclePeriodResult.getRateTypeEnum();
        return switch (rateType) {
            case FIXED -> updateFixedRateCycleStatus(cyclePeriodResult, usage);
            case TIERED -> updateTierRateUsage(cyclePeriodResult, usage, usageUnit);
            case PACKAGE -> updatePackageRateUsage(cyclePeriodResult, usage, usageUnit, existRecord);
            case USAGE -> updateUsageRateUsage(cyclePeriodResult, usage, usageUnit, existRecord);
        };
    }

    @Override
    public RateUsageVO getRateUsage(Integer rateType,
                                    Long purchaseId,
                                    Long accountId,
                                    Long serviceId,
                                    String billingCycle,
                                    String currency) {

        ChargeRateTypeEnum rateTypeEnum = ChargeRateTypeEnum.getByType(rateType);
        return switch (rateTypeEnum) {
            case FIXED -> null;
            case TIERED -> getTierRateUsage(purchaseId, serviceId, billingCycle, currency);
            case PACKAGE -> getPackageRateUsage(purchaseId, serviceId, billingCycle, currency);
            case USAGE -> getUsageRateUsage(purchaseId, serviceId, billingCycle, currency);
        };
    }

    @Override
    public boolean isFixedRateBilled(Long purchaseId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<CostFixedRateCycleStatusDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CostFixedRateCycleStatusDO::getId);
        queryWrapper.eq(CostFixedRateCycleStatusDO::getServiceId, serviceId)
                .eq(CostFixedRateCycleStatusDO::getPurchaseId, purchaseId)
                .eq(CostFixedRateCycleStatusDO::getBillingCycle, billingCycle);

        CostFixedRateCycleStatusDO statusDO = costFixedRateCycleStatusMapper.selectOne(queryWrapper);
        return Objects.nonNull(statusDO);
    }

    @Override
    public boolean isTieredRateBilled(Long purchaseId, Long serviceId, String billingCycle) {
        LambdaQueryWrapper<CostTierRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CostTierRateUsageDO::getId);
        queryWrapper.eq(CostTierRateUsageDO::getServiceId, serviceId)
                .eq(CostTierRateUsageDO::getPurchaseId, purchaseId)
                .eq(CostTierRateUsageDO::getBillingCycle, billingCycle);

        CostTierRateUsageDO statusDO = costTierRateUsageMapper.selectOne(queryWrapper);
        return Objects.nonNull(statusDO);
    }

    public RateUsageVO getTierRateUsage(Long purchaseId, Long serviceId, String billingCycle, String currency) {
        LambdaQueryWrapper<CostTierRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CostTierRateUsageDO::getTotalUsage);
        queryWrapper.eq(CostTierRateUsageDO::getServiceId, serviceId)
                .eq(CostTierRateUsageDO::getPurchaseId, purchaseId)
                .eq(CostTierRateUsageDO::getBillingCycle, billingCycle);

        CostTierRateUsageDO rateUsageDO = costTierRateUsageMapper.selectOne(queryWrapper);
        log.info("获取成本阶梯费率用量, purchaseId: {}, billingCycle: {}, currency: {}, totalUsage: {}",
                purchaseId, billingCycle, currency, JsonUtils.toJsonString(rateUsageDO));
        if (Objects.nonNull(rateUsageDO)) {
            return RateUsageVO.build(rateUsageDO.getTotalUsage(), true);
        }
        return RateUsageVO.build(BigDecimal.ZERO, true);
    }

    public RateUsageVO getPackageRateUsage(Long purchaseId, Long serviceId, String billingCycle, String currency) {
        LambdaQueryWrapper<CostPackageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CostPackageRateUsageDO::getTotalUsage);
        queryWrapper.eq(CostPackageRateUsageDO::getServiceId, serviceId)
                .eq(CostPackageRateUsageDO::getPurchaseId, purchaseId)
                .eq(CostPackageRateUsageDO::getBillingCycle, billingCycle);

        CostPackageRateUsageDO rateUsageDO = costPackageRateUsageMapper.selectOne(queryWrapper);
        log.info("获取成本套餐费率用量, purchaseId: {}, billingCycle: {}, currency: {}, totalUsage: {}",
                purchaseId, billingCycle, currency, JsonUtils.toJsonString(rateUsageDO));
        if (Objects.nonNull(rateUsageDO)) {
            return RateUsageVO.build(rateUsageDO.getTotalUsage(), true);
        }
        return RateUsageVO.build(BigDecimal.ZERO, true);
    }

    public RateUsageVO getUsageRateUsage(Long purchaseId, Long serviceId, String billingCycle, String currency) {
        LambdaQueryWrapper<CostUsageRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CostUsageRateUsageDO::getTotalUsage);
        queryWrapper.eq(CostUsageRateUsageDO::getServiceId, serviceId)
                .eq(CostUsageRateUsageDO::getPurchaseId, purchaseId)
                .eq(CostUsageRateUsageDO::getBillingCycle, billingCycle);

        CostUsageRateUsageDO rateUsageDO = costUsageRateUsageMapper.selectOne(queryWrapper);
        log.info("获取成本按量费率用量, purchaseId: {}, billingCycle: {}, currency: {}, totalUsage: {}",
                purchaseId, billingCycle, currency, JsonUtils.toJsonString(rateUsageDO));
        if (Objects.nonNull(rateUsageDO)) {
            return RateUsageVO.build(rateUsageDO.getTotalUsage(), true);
        }
        return RateUsageVO.build(BigDecimal.ZERO, true);
    }

    /**
     * 更新固定费率周期状态
     */
    private boolean updateFixedRateCycleStatus(CyclePeriodResultVO cyclePeriodResult, BigDecimal usage) {
        Long purchaseId = cyclePeriodResult.getSubscriptionId();
        Long accountId = cyclePeriodResult.getAccountId();
        Long serviceId = cyclePeriodResult.getServiceId();
        String billingCycle = cyclePeriodResult.getBillingCycle();
        LambdaQueryWrapper<CostFixedRateCycleStatusDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostFixedRateCycleStatusDO::getServiceId, serviceId)
                .eq(CostFixedRateCycleStatusDO::getPurchaseId, purchaseId)
                .eq(CostFixedRateCycleStatusDO::getBillingCycle, billingCycle);

        CostFixedRateCycleStatusDO existingRecord = costFixedRateCycleStatusMapper.selectOne(queryWrapper);

        if (existingRecord == null) {
            CostFixedRateCycleStatusDO statusDO = CostFixedRateCycleStatusDO.builder()
                    .purchaseId(purchaseId)
                    .accountId(accountId)
                    .serviceId(serviceId)
                    .billingCycle(billingCycle)
                    .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                    .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                    .totalUsage(usage)
                    .billingTime(System.currentTimeMillis())
                    .currency(cyclePeriodResult.getCurrency())
                    .createTime(System.currentTimeMillis())
                    .build();

            int insertCount = costFixedRateCycleStatusMapper.insert(statusDO);
            log.info("创建成本固定费率周期状态, purchaseId: {}, billingCycle: {}, result: {}",
                    purchaseId, billingCycle, insertCount > 0 ? "成功" : "失败");
            return insertCount > 0;
        }
        log.debug("固定费率周期状态记录已存在, purchaseId: {}, billingCycle: {}", purchaseId, billingCycle);
        return true;
    }

    /**
     * 更新阶梯费率用量
     */
    private boolean updateTierRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                        BigDecimal usage,
                                        String usageUnit) {
        Long purchaseId = cyclePeriodResult.getSubscriptionId();
        Long accountId = cyclePeriodResult.getAccountId();
        Long serviceId = cyclePeriodResult.getServiceId();
        String billingCycle = cyclePeriodResult.getBillingCycle();
        LambdaQueryWrapper<CostTierRateUsageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostTierRateUsageDO::getServiceId, serviceId)
                .eq(CostTierRateUsageDO::getPurchaseId, purchaseId)
                .eq(CostTierRateUsageDO::getBillingCycle, billingCycle);

        CostTierRateUsageDO existingRecord = costTierRateUsageMapper.selectOne(queryWrapper);

        if (existingRecord == null) {
            CostTierRateUsageDO usageDO = CostTierRateUsageDO.builder()
                    .purchaseId(purchaseId)
                    .accountId(accountId)
                    .serviceId(serviceId)
                    .billingCycle(billingCycle)
                    .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                    .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                    .totalUsage(usage)
                    .usageUnit(usageUnit)
                    .currency(cyclePeriodResult.getCurrency())
                    .createTime(System.currentTimeMillis())
                    .build();

            int insertCount = costTierRateUsageMapper.insert(usageDO);
            log.info("创建成本阶梯费率用量记录, purchaseId: {}, billingCycle: {}, usage: {}, result: {}",
                    purchaseId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
            return insertCount > 0;
        }

        // 更新累计用量
        BigDecimal newTotalUsage = existingRecord.getTotalUsage().add(usage);
        LambdaUpdateWrapper<CostTierRateUsageDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CostTierRateUsageDO::getId, existingRecord.getId())
                .set(CostTierRateUsageDO::getTotalUsage, newTotalUsage);

        int updateCount = costTierRateUsageMapper.update(null, updateWrapper);
        log.info("更新成本阶梯费率用量, purchaseId: {}, billingCycle: {}, 原用量: {}, 新增用量: {}, 总用量: {}, result: {}",
                purchaseId, billingCycle, existingRecord.getTotalUsage(), usage, newTotalUsage,
                updateCount > 0 ? "成功" : "失败");
        return updateCount > 0;
    }

    /**
     * 更新套餐费率用量
     */
    private boolean updatePackageRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                           BigDecimal usage,
                                           String usageUnit,
                                           Boolean existRecord) {
        Long purchaseId = cyclePeriodResult.getSubscriptionId();
        Long accountId = cyclePeriodResult.getAccountId();
        Long serviceId = cyclePeriodResult.getServiceId();
        String billingCycle = cyclePeriodResult.getBillingCycle();

        if (Boolean.TRUE.equals(existRecord)) {
            // 记录已存在，直接在SQL中累加字段
            int updateCount = costPackageRateUsageMapper.updateTotalUsageByCondition(serviceId, purchaseId, billingCycle, usage);
            log.info("更新成本套餐费率用量, purchaseId: {}, billingCycle: {}, 新增用量: {}, result: {}",
                    purchaseId, billingCycle, usage, updateCount > 0 ? "成功" : "失败");
            return updateCount > 0;
        }

        // 记录不存在，直接新增
        CostPackageRateUsageDO usageDO = CostPackageRateUsageDO.builder()
                .purchaseId(purchaseId)
                .accountId(accountId)
                .serviceId(serviceId)
                .billingCycle(billingCycle)
                .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                .totalUsage(usage)
                .usageUnit(usageUnit)
                .currency(cyclePeriodResult.getCurrency())
                .createTime(System.currentTimeMillis())
                .build();

        int insertCount = costPackageRateUsageMapper.insert(usageDO);
        log.info("创建成本套餐费率用量记录, purchaseId: {}, billingCycle: {}, usage: {}, result: {}",
                purchaseId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
        return insertCount > 0;
    }

    /**
     * 更新按量费率用量
     */
    private boolean updateUsageRateUsage(CyclePeriodResultVO cyclePeriodResult,
                                         BigDecimal usage,
                                         String usageUnit,
                                         Boolean existRecord) {
        Long purchaseId = cyclePeriodResult.getSubscriptionId();
        Long accountId = cyclePeriodResult.getAccountId();
        Long serviceId = cyclePeriodResult.getServiceId();
        String billingCycle = cyclePeriodResult.getBillingCycle();

        if (Boolean.TRUE.equals(existRecord)) {
            // 记录已存在，直接在SQL中累加字段
            int updateCount = costUsageRateUsageMapper.updateTotalUsageByCondition(serviceId, purchaseId, billingCycle, usage);
            log.info("更新成本按量费率用量, purchaseId: {}, billingCycle: {}, 新增用量: {}, result: {}",
                    purchaseId, billingCycle, usage, updateCount > 0 ? "成功" : "失败");
            return updateCount > 0;
        }

        // 记录不存在，直接新增
        CostUsageRateUsageDO usageDO = CostUsageRateUsageDO.builder()
                .purchaseId(purchaseId)
                .accountId(accountId)
                .serviceId(serviceId)
                .billingCycle(billingCycle)
                .billingCycleStart(cyclePeriodResult.getCycleStartTime())
                .billingCycleEnd(cyclePeriodResult.getCycleEndTime())
                .totalUsage(usage)
                .usageUnit(usageUnit)
                .currency(cyclePeriodResult.getCurrency())
                .createTime(System.currentTimeMillis())
                .build();

        int insertCount = costUsageRateUsageMapper.insert(usageDO);
        log.info("创建成本按量费率用量记录, purchaseId: {}, billingCycle: {}, usage: {}, result: {}",
                purchaseId, billingCycle, usage, insertCount > 0 ? "成功" : "失败");
        return insertCount > 0;
    }
}
