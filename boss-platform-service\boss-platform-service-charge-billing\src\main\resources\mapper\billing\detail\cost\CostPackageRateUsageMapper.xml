<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkcircle.boss.module.billing.web.detail.rate.cost.mapper.CostPackageRateUsageMapper">
    <!-- 累加总用量 -->
    <update id="updateTotalUsageByCondition">
        UPDATE cost_package_rate_usage
        SET total_usage = total_usage + #{usage}
        WHERE service_id = #{serviceId}
        AND purchase_id = #{purchaseId}
        AND billing_cycle = #{billingCycle}
    </update>
</mapper>
