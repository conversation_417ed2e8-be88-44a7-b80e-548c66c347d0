package com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.impl;

import com.linkcircle.boss.framework.common.exception.util.ServiceExceptionUtil;
import com.linkcircle.boss.module.billing.api.rate.model.dto.UsageBasedRateConfigDTO;
import com.linkcircle.boss.module.billing.constants.ErrorCodeConstants;
import com.linkcircle.boss.module.billing.constants.RateTypeConstant;
import com.linkcircle.boss.module.billing.enums.CostRateTypeEnum;
import com.linkcircle.boss.module.billing.enums.OriginalPriceRateTypeEnum;
import com.linkcircle.boss.module.billing.web.data.model.vo.CyclePeriodResultVO;
import com.linkcircle.boss.module.billing.web.data.service.CyclePeriodCalculateService;
import com.linkcircle.boss.module.billing.web.detail.cost.model.dto.ResourceSubscriptionInfoDTO;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.AbstractCostRateChargeStrategy;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.context.CostRateChargeRequest;
import com.linkcircle.boss.module.billing.web.detail.cost.service.calculation.strategy.context.CostRateChargeResponse;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.model.vo.RateUsageVO;
import com.linkcircle.boss.module.billing.web.detail.rate.cost.service.CostRateUsageManageService;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateRequest;
import com.linkcircle.boss.module.billing.web.detail.rate.strategy.context.OriginalPriceCalculateResponse;
import com.linkcircle.boss.module.crm.api.supplier.account.vo.SupplierAccountVO;
import com.linkcircle.boss.module.crm.api.supplier.purchase.vo.ResourcePurchaseVO;
import com.linkcircle.boss.module.crm.enums.ChargeRateTypeEnum;
import io.github.kk01001.design.pattern.strategy.IStrategy;
import io.github.kk01001.design.pattern.strategy.StrategyFactory;
import io.github.kk01001.design.pattern.strategy.annotation.Strategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-24 16:10
 * @description 成本按量费率计费策略
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Strategy(strategyEnum = CostRateTypeEnum.class, strategyType = RateTypeConstant.USAGE)
public class CostUsageRateChargeStrategy extends AbstractCostRateChargeStrategy implements IStrategy<CostRateChargeRequest, CostRateChargeResponse> {

    private final CyclePeriodCalculateService cyclePeriodCalculateService;
    private final CostRateUsageManageService costRateUsageManageService;
    private final StrategyFactory strategyFactory;

    @Override
    protected CyclePeriodCalculateService getCyclePeriodCalculateService() {
        return cyclePeriodCalculateService;
    }

    @Override
    public CostRateChargeResponse execute(CostRateChargeRequest request) {
        log.info("开始执行成本按量费率计费策略");

        // 1. 获取基础数据
        ResourceSubscriptionInfoDTO resourceInfo = request.getResourceSubscriptionInfoDTO();
        ResourcePurchaseVO purchaseVO = resourceInfo.getPurchaseVO();
        SupplierAccountVO supplierAccountVO = request.getSupplierAccountVO();
        ResourcePurchaseVO.Detail detail = resourceInfo.getDetail();
        BigDecimal currentUsage = request.getUsage();
        String usageUnit = request.getUsageUnit();
        String accountCurrency = supplierAccountVO.getCurrency();

        // 2. 解析按量费率配置
        UsageBasedRateConfigDTO rateConfig = getUsageBasedRateConfigDTO(detail.getCurrencyPriceJson());

        // 4. 计算计费周期
        Long businessTime = request.getBusinessTime();
        CyclePeriodResultVO cyclePeriodResultVO = getBillingCycle(resourceInfo, supplierAccountVO, businessTime);

        // 5. 获取当前周期累计用量（按量计费以账户为维度）
        RateUsageVO rateUsageVO = costRateUsageManageService.getRateUsage(ChargeRateTypeEnum.USAGE, cyclePeriodResultVO);
        BigDecimal previousUsage = rateUsageVO.getUsageCount();
        BigDecimal totalUsageWithCurrent = previousUsage.add(currentUsage);

        // 6. 计算原价
        OriginalPriceCalculateResponse calculateResponse = calculateOriginalPriceByStrategy(purchaseVO, totalUsageWithCurrent,
                previousUsage, currentUsage, usageUnit, rateConfig, accountCurrency, detail.getPaymentOptions());
        BigDecimal originalPrice = calculateResponse.getOriginalPrice();

        // 8. db更新费率用量
        costRateUsageManageService.updateRateUsage(cyclePeriodResultVO, currentUsage, usageUnit, rateUsageVO.getExist());

        // 9. 构建响应结果
        CostRateChargeResponse response = CostRateChargeResponse.success();
        response.setCurrency(accountCurrency);
        response.setUsage(currentUsage);
        response.setUsageUnit(rateConfig.getMeasureUnit());
        response.setCyclePeriodResultVO(cyclePeriodResultVO);
        response.setRateConfig(rateConfig);
        convertChargeResponse(response, calculateResponse);

        log.info("成本按量费率计费策略执行完成，原价: {}", originalPrice);
        return response;
    }

    /**
     * 通过策略计算按量费率目录价
     *
     * @param totalUsageWithCurrent 包含本次用量的总累计用量
     * @param previousUsage         之前的累计用量
     * @param currentUsage          本次用量
     * @param rateConfig            按量计费配置
     * @param currency              币种
     * @param paymentOptions        支付方式 0-现金 1-积分
     * @return 本次计费金额
     */
    private OriginalPriceCalculateResponse calculateOriginalPriceByStrategy(ResourcePurchaseVO purchaseVO,
                                                                            BigDecimal totalUsageWithCurrent,
                                                                            BigDecimal previousUsage,
                                                                            BigDecimal currentUsage,
                                                                            String usageUnit,
                                                                            UsageBasedRateConfigDTO rateConfig,
                                                                            String currency,
                                                                            Integer paymentOptions) {
        // 构建原价计算请求
        OriginalPriceCalculateRequest request = new OriginalPriceCalculateRequest();
        request.setTotalUsageWithCurrent(totalUsageWithCurrent);
        request.setPreviousUsage(previousUsage);
        request.setCurrentUsage(currentUsage);
        request.setCurrentUsageUnit(usageUnit);
        request.setRateConfig(rateConfig);
        request.setCurrency(currency);
        request.setPaymentOptions(paymentOptions);
        request.setCalculateTaxEnabled(purchaseVO.getIsTaxInclusive());
        request.setTaxRate(purchaseVO.getRate());

        // 调用原价计算策略
        OriginalPriceCalculateResponse response = strategyFactory.execute(OriginalPriceRateTypeEnum.class,
                OriginalPriceRateTypeEnum.USAGE.name(), request);

        if (response.getSuccess()) {
            return response;
        }
        log.error("原价计算失败: {}", response.getErrorMessage());
        throw ServiceExceptionUtil.exception(ErrorCodeConstants.BILLING_CALCULATION_FAILED, response.getErrorMessage());
    }
}
